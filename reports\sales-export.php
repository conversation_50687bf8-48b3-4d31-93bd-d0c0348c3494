<?php

use App\Models\Sale;
use App\Models\Customer;
use App\Models\Branch;
use App\Models\Payment;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

require __DIR__."/../php/init.php";

if (!can("view-reports")) {
    die("403");
}

// Get filter parameters (same as main report)
$_from = input("from", date("Y-m-01"));
$_to = input("to", date("Y-m-t"));
$_status = input("status", "");
$_customer_id = input("customer_id", "");
$_branch_id = input("branch_id", "");

// Build query (same logic as main report)
$query = Sale::query()
    ->with(['customer', 'branch', 'createdBy'])
    ->whereBetween('date', [$_from, $_to]);

if (!empty($_status)) {
    $query->where('status', $_status);
}

if (!empty($_customer_id)) {
    $query->where('customer_id', $_customer_id);
}

if (!empty($_branch_id)) {
    $query->where('branch_id', $_branch_id);
} else {
    if (!can("branches")) {
        $query->where('branch_id', _branch()->id());
    }
}

$sales = $query->orderBy('date', 'desc')->get();

// Create spreadsheet
$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();

// Set headers
$headers = [
    'ID',
    'Date',
    'Customer',
    'Total Price',
    'Tax Amount',
    'Status',
    'Payment Status',
    'Paid Amount',
    'Balance',
];

if (can("branches")) {
    $headers[] = 'Branch';
}

$headers = array_merge($headers, ['Created By', 'Invoice Number', 'Notes']);

// Write headers
$sheet->fromArray($headers, null, 'A1');

// Style headers
$headerStyle = [
    'font' => ['bold' => true],
    'fill' => [
        'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
        'startColor' => ['rgb' => 'E2E8F0']
    ]
];
$sheet->getStyle('A1:' . chr(64 + count($headers)) . '1')->applyFromArray($headerStyle);

// Write data
$row = 2;
foreach ($sales as $sale) {
    $paid_amount = Payment::where('reltype', Payment::RELTYPE_SALES)
                          ->where('relid', $sale->id)
                          ->sum('amount');
    
    $balance = $sale->total_price - $paid_amount;
    
    // Determine payment status
    if ($paid_amount == 0) {
        $payment_status = 'Unpaid';
    } elseif ($paid_amount < $sale->total_price) {
        $payment_status = 'Partially Paid';
    } elseif ($paid_amount == $sale->total_price) {
        $payment_status = 'Paid';
    } else {
        $payment_status = 'Overpaid';
    }

    $data = [
        $sale->id,
        _d($sale->date),
        $sale->customer ? $sale->customer->name : 'Walk-in Customer',
        $sale->total_price,
        $sale->total_tax,
        $sale->status,
        $payment_status,
        $paid_amount,
        $balance,
    ];

    if (can("branches")) {
        $data[] = $sale->branch ? $sale->branch->name : '-';
    }

    $data = array_merge($data, [
        $sale->createdBy ? $sale->createdBy->name : '-',
        $sale->invoice_number ?: '-',
        $sale->admin_note ?: '-'
    ]);

    $sheet->fromArray($data, null, 'A' . $row);
    $row++;
}

// Auto-size columns
foreach (range('A', chr(64 + count($headers))) as $col) {
    $sheet->getColumnDimension($col)->setAutoSize(true);
}

// Add summary at the bottom
$row += 2;
$sheet->setCellValue('A' . $row, 'SUMMARY');
$sheet->getStyle('A' . $row)->getFont()->setBold(true);

$row++;
$sheet->setCellValue('A' . $row, 'Total Sales:');
$sheet->setCellValue('B' . $row, $sales->count());

$row++;
$sheet->setCellValue('A' . $row, 'Total Revenue:');
$sheet->setCellValue('B' . $row, $sales->sum('total_price'));

$row++;
$sheet->setCellValue('A' . $row, 'Total Tax:');
$sheet->setCellValue('B' . $row, $sales->sum('total_tax'));

$row++;
$sheet->setCellValue('A' . $row, 'Average Order Value:');
$sheet->setCellValue('B' . $row, $sales->count() > 0 ? $sales->sum('total_price') / $sales->count() : 0);

// Set filename
$filename = 'sales_report_' . $_from . '_to_' . $_to . '.xlsx';

// Output file
header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
header('Content-Disposition: attachment;filename="' . $filename . '"');
header('Cache-Control: max-age=0');

$writer = new Xlsx($spreadsheet);
$writer->save('php://output');
exit;
