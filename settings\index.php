<?php
// Models
require(__DIR__."/../php/init.php");

// Page settings
$page = [
    "title" => tr("Settings"),
    "active" => "settings", // sidebar active link
];

// Check permissions
if (!can("settings")) {
    die("403");
}

// Page navigation
$page_nav = [
    "reload" => 1, // show reload button
    "back" => base_url("dashboard.php"), // show back button
    "breadcrumb" => [ // show breadcrumb
        tr("Home") => base_url("dashboard.php"),
        tr("Settings") => "",
    ],
];

// Get current category from URL parameter
$current_category = input('category', 'business');

// Define settings categories
$categories = [
    'business' => [
        'title' => tr('Business Settings'),
        'icon' => 'icon-cog',
        'sections' => [

            [
                "title"=>"General",
                "settings"=>[
                    'app_name' => ['type' => 'text', 'label' => tr('Application Name'), 'default' => 'StockV3'],
                    'app_primary_color' => ['type' => 'color', 'label' => tr('Primary Color'), 'default' => '#007bff'],
                    'app_secondary_color' => ['type' => 'color', 'label' => tr('Secondary Color'), 'default' => '#6c757d'],
                    'price_include_tax' => ['type' => 'select', 'label' => tr('Price Include Tax'), 'options' => ['0' => tr('Exclude Tax'), '1' => tr('Include Tax')], 'default' => '0'],
                    'number_separator' => ['type' => 'text', 'label' => tr('Number Separator'), 'default' => ','],
                    'float_points' => ['type' => 'number', 'label' => tr('Decimal Points'), 'default' => '3'],
                    'currency_symbol' => ['type' => 'text', 'label' => tr('Currency Symbol'), 'default' => 'OMR'],
                    'currency_position' => ['type' => 'select', 'label' => tr('Currency Position'), 'options' => ['left' => tr('Left'), 'right' => tr('Right')], 'default' => 'right'],
                    'app_logo' => ['type' => 'file', 'label' => tr('Logo'), 'default' => ''],
                ]


            ],



        ]
    ],
    'pos' => [
        'title' => tr('POS Settings'),
        'icon' => 'icon-shopping-cart',
        'sections' => [
            [
                "title" => "Keyboard Shortcuts",
                "settings" => [
                    'pos_shortcut_express_checkout' => ['type' => 'text', 'label' => tr('Express Checkout'), 'default' => 'shift+e', 'col' => 'col-md-6', 'help' => tr('Use key names: shift, ctrl, alt, backspace, tab, enter, return, capslock, esc, escape, space, pageup, pagedown, end, home, left, up, right, down, ins, del, plus')],
                    'pos_shortcut_pay_checkout' => ['type' => 'text', 'label' => tr('Pay & Checkout'), 'default' => 'shift+p', 'col' => 'col-md-6'],
                    'pos_shortcut_draft' => ['type' => 'text', 'label' => tr('Draft'), 'default' => 'shift+d', 'col' => 'col-md-6'],
                    'pos_shortcut_cancel' => ['type' => 'text', 'label' => tr('Cancel'), 'default' => 'shift+c', 'col' => 'col-md-6'],
                    'pos_shortcut_product_quantity' => ['type' => 'text', 'label' => tr('Go to product quantity'), 'default' => 'f2', 'col' => 'col-md-6'],
                    'pos_shortcut_edit_discount' => ['type' => 'text', 'label' => tr('Edit Discount'), 'default' => 'shift+i', 'col' => 'col-md-6'],
                    'pos_shortcut_edit_tax' => ['type' => 'text', 'label' => tr('Edit Order Tax'), 'default' => 'shift+t', 'col' => 'col-md-6'],
                    'pos_shortcut_add_payment' => ['type' => 'text', 'label' => tr('Add Payment Row'), 'default' => 'shift+r', 'col' => 'col-md-6'],
                    'pos_shortcut_finalize_payment' => ['type' => 'text', 'label' => tr('Finalize Payment'), 'default' => 'shift+f', 'col' => 'col-md-6'],
                    'pos_shortcut_add_product' => ['type' => 'text', 'label' => tr('Add new product'), 'default' => 'f4', 'col' => 'col-md-6'],
                ]
            ],
            [
                "title" => "POS Options",
                "settings" => [
                    "pos_auto_add_product_on_search" => ['type' => 'checkbox', 'label' => tr('Auto add product on search'), 'default' => '0', 'col' => 'col-md-6'],
                    'pos_disable_multiple_pay' => ['type' => 'checkbox', 'label' => tr('Disable Multiple Pay'), 'default' => '0', 'col' => 'col-md-6'],
                    'pos_disable_draft' => ['type' => 'checkbox', 'label' => tr('Disable Draft'), 'default' => '0', 'col' => 'col-md-6'],
                    'pos_hide_product_suggestion' => ['type' => 'checkbox', 'label' => tr('Don\'t show product suggestion'), 'default' => '0', 'col' => 'col-md-6'],
                    'pos_hide_recent_transactions' => ['type' => 'checkbox', 'label' => tr('Don\'t show recent transactions'), 'default' => '0', 'col' => 'col-md-6'],
                    'pos_disable_discount' => ['type' => 'checkbox', 'label' => tr('Disable Discount'), 'default' => '0', 'col' => 'col-md-6'],
                    'pos_disable_order_tax' => ['type' => 'checkbox', 'label' => tr('Disable order tax'), 'default' => '0', 'col' => 'col-md-6'],
                    'pos_subtotal_editable' => ['type' => 'checkbox', 'label' => tr('Subtotal Editable'), 'default' => '0', 'col' => 'col-md-6'],
                    'pos_enable_transaction_date' => ['type' => 'checkbox', 'label' => tr('Enable transaction date on POS screen'), 'default' => '0', 'col' => 'col-md-6'],
                    'pos_enable_service_staff' => ['type' => 'checkbox', 'label' => tr('Enable service staff in product line'), 'default' => '0', 'col' => 'col-md-6'],
                    'pos_service_staff_required' => ['type' => 'checkbox', 'label' => tr('Is service staff required'), 'default' => '0', 'col' => 'col-md-6'],
                    'pos_disable_credit_sale' => ['type' => 'checkbox', 'label' => tr('Disable credit sale button'), 'default' => '0', 'col' => 'col-md-6'],
                    'pos_enable_weighing_scale' => ['type' => 'checkbox', 'label' => tr('Enable Weighing Scale'), 'default' => '0', 'col' => 'col-md-6'],
                    'pos_show_invoice_layout' => ['type' => 'checkbox', 'label' => tr('Show invoice layout dropdown'), 'default' => '0', 'col' => 'col-md-6'],
                ]
            ],
            [
                "title" => "Weighing Scale Barcode Settings",
                "settings" => [
                    'weighing_scale_prefix' => ['type' => 'text', 'label' => tr('Prefix'), 'default' => '', 'col' => 'col-md-6', 'help' => tr('Configure barcode as per your weighing scale')],
                    'weighing_scale_sku_length' => ['type' => 'number', 'label' => tr('Product sku length'), 'default' => '6', 'col' => 'col-md-6'],
                    'weighing_scale_qty_integer_length' => ['type' => 'number', 'label' => tr('Quantity integer part length'), 'default' => '3', 'col' => 'col-md-6'],
                    'weighing_scale_qty_fractional_length' => ['type' => 'number', 'label' => tr('Quantity fractional part length'), 'default' => '3', 'col' => 'col-md-6'],
                ]
            ]
        ]
    ],







    'receipt' => [
        'title' => tr('Receipt Printing'),
        'icon' => 'icon-printer',
        'sections' => [
            [
                "title"=>"General",
                "settings"=>[
                    'print_receipt_mode' => ['type' => 'select', 'label' => tr('Receipt Printing Mode'), 'options' => ['disabled' => tr('Disabled'), 'enabled' => tr('Enabled')], 'default' => 'disabled'],
                    'print_receipt_printer' => ['type' => 'text', 'label' => tr('Default Receipt Printer'), 'default' => ''],
                    'print_receipt_format' => ['type' => 'textarea', 'label' => tr('Receipt Format Template'), 'default' => ''],
                    'print_receipt_width' => ['type' => 'number', 'label' => tr('Receipt Width (mm)'), 'default' => '80'],
                    'print_receipt_height' => ['type' => 'number', 'label' => tr('Receipt Height (mm)'), 'default' => '200'],
                ]
            ],
        ]
    ],
    'barcode' => [
        'title' => tr('Barcode Printing'),
        'icon' => 'icon-barcode',
        'sections' => [
            [
                "title"=>"General",
                "settings"=>[

                    'print_barcode_mode' => ['type' => 'select', 'label' => tr('Barcode Printing Mode'), 'options' => ['disabled' => tr('Disabled'), 'enabled' => tr('Enabled')], 'default' => 'disabled'],
                    'print_barcode_printer' => ['type' => 'text', 'label' => tr('Default Barcode Printer'), 'default' => ''],
                    'barcode_type' => ['type' => 'select', 'label' => tr('Barcode Type'), 'options' => ['C128' => 'Code 128', 'C39' => 'Code 39', 'EAN13' => 'EAN-13', 'EAN8' => 'EAN-8', 'UPCA' => 'UPC-A', 'UPCE' => 'UPC-E', 'I25' => 'Interleaved 2 of 5'], 'default' => 'C128'],
                    'barcode_page_size' => ['type' => 'select', 'label' => tr('Page Size'), 'options' => ['A4' => 'A4 (210 × 297 mm)', 'A4-L' => 'A4 Landscape', 'Letter' => 'Letter (8.5 × 11 inches)', 'Letter-L' => 'Letter Landscape', 'Continuous' => 'Continuous'], 'default' => 'A4'],
                    'barcode_labels_per_row' => ['type' => 'number', 'label' => tr('Labels Per Row'), 'default' => '3'],
                    'barcode_labels_per_column' => ['type' => 'number', 'label' => tr('Labels Per Column'), 'default' => '8'],
                    'barcode_width_factor' => ['type' => 'number', 'label' => tr('Barcode Width Factor'), 'default' => '2'],
                    'barcode_height' => ['type' => 'number', 'label' => tr('Barcode Height (mm)'), 'default' => '50'],
                    'barcode_continuous_width' => ['type' => 'number', 'label' => tr('Continuous Page Width (mm)'), 'default' => '100', 'help' => tr('Custom width for continuous printing mode')],
                    'barcode_continuous_height' => ['type' => 'number', 'label' => tr('Continuous Page Height (mm)'), 'default' => '50', 'help' => tr('Custom height for continuous printing mode')],
                    'barcode_show_name' => ['type' => 'select', 'label' => tr('Show Product Name'), 'options' => ['0' => tr('No'), '1' => tr('Yes')], 'default' => '1'],
                    'barcode_show_price' => ['type' => 'select', 'label' => tr('Show Product Price'), 'options' => ['0' => tr('No'), '1' => tr('Yes')], 'default' => '1'],
                    'barcode_show_code' => ['type' => 'select', 'label' => tr('Show Product Code'), 'options' => ['0' => tr('No'), '1' => tr('Yes')], 'default' => '1'],
                    'barcode_font_size' => ['type' => 'number', 'label' => tr('Font Size'), 'default' => '8'],
                    'barcode_copies' => ['type' => 'number', 'label' => tr('Default Copies Per Product'), 'default' => '1'],
                ],
            ]
        ]
        
    ],
    'sms_gateway' => [
        'title' => tr('SMS Gateway'),
        'icon' => 'icon-mobile',
        'sections' => [
            [
                "title"=>"General",
                "settings"=>[
                    

                    'sms_provider' => ['type' => 'select', 'label' => tr('SMS Provider'), 'options' => ['custom' => tr('Custom Gateway'), 'twilio' => 'Twilio', 'nexmo' => 'Nexmo'], 'default' => 'custom'],
                    'sms_api_key' => ['type' => 'text', 'label' => tr('API Key'), 'default' => ''],
                    'sms_api_secret' => ['type' => 'password', 'label' => tr('API Secret'), 'default' => ''],
                    'sms_endpoint_url' => ['type' => 'url', 'label' => tr('Endpoint URL'), 'default' => ''],
                    'sms_auth_method' => ['type' => 'select', 'label' => tr('Authentication Method'), 'options' => ['header' => tr('Header'), 'query' => tr('Query Parameter'), 'body' => tr('Request Body')], 'default' => 'header'],
                    'sms_rate_limit' => ['type' => 'number', 'label' => tr('Rate Limit (per minute)'), 'default' => '60'],
                    'sms_fallback_enabled' => ['type' => 'select', 'label' => tr('Enable Fallback'), 'options' => ['0' => tr('No'), '1' => tr('Yes')], 'default' => '0'],
                ]
            ]
        ]
    ],
    'sms_templates' => [
        'title' => tr('SMS Templates'),
        'icon' => 'icon-file-text',
        'sections' => [
            [
                "title"=>"General",
                "settings"=>[

                    'sms_template_order_confirmation' => ['type' => 'textarea', 'label' => tr('Order Confirmation'), 'default' => 'Your order #{order_id} has been confirmed. Total: {total}'],
                    'sms_template_delivery_notification' => ['type' => 'textarea', 'label' => tr('Delivery Notification'), 'default' => 'Your order #{order_id} is out for delivery.'],
                    'sms_template_payment_reminder' => ['type' => 'textarea', 'label' => tr('Payment Reminder'), 'default' => 'Payment reminder for invoice #{invoice_id}. Amount due: {amount}'],
                    'sms_template_low_stock' => ['type' => 'textarea', 'label' => tr('Low Stock Alert'), 'default' => 'Low stock alert: {product_name} - Only {quantity} left'],
                    'sms_template_welcome' => ['type' => 'textarea', 'label' => tr('Welcome Message'), 'default' => 'Welcome to {app_name}! Thank you for joining us.'],

        
                ]
            ]
        ]
    ],
    'branch' => [
        'title' => tr('Branch Settings'),
        'icon' => 'icon-office',
        'redirect' => base_url('settings/branch.php'),
        'sections' => [] // This will redirect to separate page
    ],

];

// Validate current category exists
if (!isset($categories[$current_category])) {
    $current_category = 'business'; // Fallback to first category
}

// Handle form submission
if (is_post()) {
    $category = input('category', 'business');

    if (!isset($categories[$category])) {
        _response([
            "success" => false,
            "message" => [tr("Invalid category")]
        ]);
    }

    // Validation rules - flatten settings from all sections
    $validation_rules = [];
    $category_settings = [];
    
    // Flatten all settings from sections into a single array
    foreach ($categories[$category]['sections'] as $section) {
        foreach ($section['settings'] as $key => $setting) {
            $category_settings[$key] = $setting;
        }
    }

    foreach ($category_settings as $key => $setting) {
        $rules = ['required'];

        if ($setting['type'] == 'email') {
            $rules[] = 'email';
        } elseif ($setting['type'] == 'url') {
            $rules[] = 'url';
        } elseif ($setting['type'] == 'number') {
            $rules[] = 'numeric';
        }

        $validation_rules[tr($setting['label'])] = [$key, implode('|', $rules)];
    }

    $v = validate($validation_rules);

    if (!$v->passes()) {
        _response([
            "success" => false,
            "message" => $v->errors()->all()
        ]);
    }

    // Save settings
    foreach ($category_settings as $key => $setting) {
        $value = input($key, $setting['default']);
        

        if($key=="app_logo"){

            $file = storage()->save($_FILES['app_logo']);
            if($file->saved()){
                $value= $file->id;
                set_option($key, $value);
            }else{
                set_option($key, $setting['default']);
            }
            
        }
    }

    _response([
        "success" => true,
        "message" => [tr("Updated successfully")],
        "action" => "location.reload()"
    ]);
}

?>

<!-- Layout start -->
<?php _layout_start() ?>

<div class="d-flex justify-content-between mb-3">
    <h3><?= tr("Settings") ?></h3>
</div>

<div class="row">
    <!-- Settings Categories Sidebar -->
    <div class="col-md-3">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title"><?= tr("Categories") ?></h6>
            </div>
            <div class="list-group list-group-flush">
                <?php foreach ($categories as $key => $category): ?>
                <?php if (isset($category['redirect'])): ?>
                <a href="<?= $category['redirect'] ?>"
                    class="list-group-item list-group-item-action">
                    <i class="<?= $category['icon'] ?> mr-2"></i>
                    <?= $category['title'] ?>
                </a>
                <?php else: ?>
                <a href="<?= base_url('settings') ?>?category=<?= $key ?>"
                    class="list-group-item list-group-item-action <?= $current_category === $key ? 'active' : '' ?>">
                    <i class="<?= $category['icon'] ?> mr-2"></i>
                    <?= $category['title'] ?>
                </a>
                <?php endif; ?>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <!-- Settings Form -->
    <div class="col-md-9">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title">
                    <i class="<?= $categories[$current_category]['icon'] ?> mr-2"></i>
                    <?= $categories[$current_category]['title'] ?>
                </h6>
            </div>
            <div class="card-body">
                <form method="post" class="ajax">
                    <?= csrf()->getTokenInputField() ?>
                    <input type="hidden" name="category" value="<?= $current_category ?>">

                    <?php foreach ($categories[$current_category]['sections'] as $section): ?>
                        <?php if (count($categories[$current_category]['sections']) > 1): ?>
                        <div class="mb-4">
                            <h5 class="border-bottom pb-2 mb-3"><?= $section['title'] ?></h5>
                        </div>
                        <?php endif; ?>

                        <div class="row">
                        <?php foreach ($section['settings'] as $key => $setting): ?>
                        <div class="<?= isset($setting['col']) ? $setting['col'] : 'col-md-6' ?> <?= $setting['type'] == 'checkbox' ? 'col-12' : '' ?>">
                            <div class="form-group">
                                <?php if ($setting['type'] == 'checkbox'): ?>
                                <div class="form-check">
                                    <input type="hidden" name="<?= $key ?>" value="0">
                                    <input type="checkbox" class="form-check-input" id="<?= $key ?>" name="<?= $key ?>" value="1"
                                        <?= get_option($key, $setting['default']) == '1' ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="<?= $key ?>">
                                        <?= $setting['label'] ?>
                                    </label>
                                </div>
                                <?php else: ?>
                                <label for="<?= $key ?>"><?= $setting['label'] ?></label>

                                <?php if ($setting['type'] == 'text'): ?>
                                <input type="text" class="form-control" id="<?= $key ?>" name="<?= $key ?>"
                                    value="<?= escap(get_option($key, $setting['default'])) ?>"
                                    placeholder="<?= $setting['label'] ?>">

                                <?php elseif ($setting['type'] == 'password'): ?>
                                <input type="password" class="form-control" id="<?= $key ?>" name="<?= $key ?>"
                                    value="<?= escap(get_option($key, $setting['default'])) ?>"
                                    placeholder="<?= $setting['label'] ?>">

                                <?php elseif ($setting['type'] == 'email'): ?>
                                <input type="email" class="form-control" id="<?= $key ?>" name="<?= $key ?>"
                                    value="<?= escap(get_option($key, $setting['default'])) ?>"
                                    placeholder="<?= $setting['label'] ?>">

                                <?php elseif ($setting['type'] == 'url'): ?>
                                <input type="url" class="form-control" id="<?= $key ?>" name="<?= $key ?>"
                                    value="<?= escap(get_option($key, $setting['default'])) ?>"
                                    placeholder="<?= $setting['label'] ?>">

                                <?php elseif ($setting['type'] == 'number'): ?>
                                <input type="number" class="form-control" id="<?= $key ?>" name="<?= $key ?>"
                                    value="<?= escap(get_option($key, $setting['default'])) ?>"
                                    placeholder="<?= $setting['label'] ?>">

                                <?php elseif ($setting['type'] == 'color'): ?>
                                <input type="color" class="form-control" id="<?= $key ?>" name="<?= $key ?>"
                                    value="<?= escap(get_option($key, $setting['default'])) ?>">

                                <?php elseif ($setting['type'] == 'select'): ?>
                                <select class="form-control" id="<?= $key ?>" name="<?= $key ?>">
                                    <?php foreach ($setting['options'] as $option_value => $option_label): ?>
                                    <option value="<?= $option_value ?>"
                                        <?= get_option($key, $setting['default']) == $option_value ? 'selected' : '' ?>>
                                        <?= $option_label ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>

                                <?php elseif ($setting['type'] == 'textarea'): ?>
                                <textarea class="form-control" id="<?= $key ?>" name="<?= $key ?>" rows="4"
                                    placeholder="<?= $setting['label'] ?>"><?= escap(get_option($key, $setting['default'])) ?></textarea>

                                <?php elseif ($setting['type'] == 'file'): ?>
                                <input type="file" class="form-control" id="<?= $key ?>" name="<?= $key ?>">
                                <img src="<?= storage()->get(get_option($key, $setting['default'])) ?>" alt=""
                                    style=" height: 100px;">
                                <?php endif; ?>
                                <?php endif; ?>

                                <?php if (isset($setting['help'])): ?>
                                <small class="form-text text-muted"><?= $setting['help'] ?></small>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php endforeach; ?>
                        </div>
                    <?php endforeach; ?>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="icon-checkmark mr-2"></i>
                            <?= tr("Save Settings") ?>
                        </button>
                        <button type="button" class="btn btn-secondary ml-2" onclick="location.reload()">
                            <i class="icon-reload-alt mr-2"></i>
                            <?= tr("Reset") ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Handle form submission
    $('.ajax').on('submit', function(e) {
        e.preventDefault();

        var form = $(this);
        var submitBtn = form.find('button[type="submit"]');
        var originalText = submitBtn.html();

        // Disable submit button and show loading
        submitBtn.prop('disabled', true).html('<i class="icon-spinner2 spinner mr-2"></i>' +
            '<?= tr("Saving...") ?>');


    });
});
</script>

<!-- Layout end -->
<?php _layout_end() ?>