<?php

require __DIR__."/php/init.php";

$page=[
	"title"=>tr("Dashboard"),
	"active"=>"dashboard",
];

// Get user information for welcome section
$user = auth();
$branch = _branch();

?>
<?php _layout_start() ?>

<!-- Welcome Section -->
<div class="row mb-4">
	<div class="col-lg-8">
		<div class="card bg-gradient-primary text-white">
			<div class="card-body">
				<div class="d-flex align-items-center">
					<div class="flex-grow-1">
						<h4 class="text-white mb-1"><?= tr("Welcome back") ?>, <?= esc($user->name) ?>!</h4>
						<p class="text-white-50 mb-0"><?= tr("Branch") ?>: <?= esc($branch->name()) ?></p>
					</div>
					<div class="flex-shrink-0">
						<i class="fas fa-tachometer-alt fa-3x text-white-50"></i>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="col-lg-4">
		<div class="card">
			<div class="card-body text-center">
				<h6 class="text-muted mb-1"><?= tr("Today") ?></h6>
				<h5 class="mb-0"><?= date('l, F j, Y') ?></h5>
				<small class="text-muted current-time"><?= date('g:i A') ?></small>
			</div>
		</div>
	</div>
</div>

<!-- Quick Actions Section -->
<div class="row mb-4">
	<div class="col-12">
		<h5 class="mb-3"><?= tr("Quick Actions") ?></h5>
	</div>
</div>

<!-- Primary Operations -->
<div class="row g-3 mb-4">
	<?php if (can("view-products")): ?>
	<div class="col-lg-3 col-md-4 col-sm-6">
		<a href="<?= base_url('products/') ?>" id="products-shortcut" class="text-decoration-none">
			<div class="card h-100 border-0 shadow-sm transition-hover">
				<div class="card-body text-center p-4">
					<div class="icon-circle bg-primary-light text-primary mx-auto mb-3">
						<i class="fas fa-barcode"></i>
					</div>
					<h6 class="mb-1 text-dark"><?= tr("Products") ?></h6>
					<small class="text-muted"><?= tr("Manage inventory") ?> (Ctrl+P)</small>
				</div>
			</div>
		</a>
	</div>
	<?php endif; ?>

	<?php if (can("manage-products")): ?>
	<div class="col-lg-3 col-md-4 col-sm-6">
		<a href="<?= base_url('products/categories.php') ?>" class="text-decoration-none">
			<div class="card h-100 border-0 shadow-sm transition-hover">
				<div class="card-body text-center p-4">
					<div class="icon-circle bg-primary-light text-primary mx-auto mb-3">
						<i class="fas fa-tags"></i>
					</div>
					<h6 class="mb-1 text-dark"><?= tr("Categories") ?></h6>
					<small class="text-muted"><?= tr("Product categories") ?></small>
				</div>
			</div>
		</a>
	</div>
	<?php endif; ?>

	<?php if (can("view-sales")): ?>
	<div class="col-lg-3 col-md-4 col-sm-6">
		<a href="<?= base_url('sales/') ?>" id="sales-shortcut" class="text-decoration-none">
			<div class="card h-100 border-0 shadow-sm transition-hover">
				<div class="card-body text-center p-4">
					<div class="icon-circle bg-success-light text-success mx-auto mb-3">
						<i class="fas fa-money-bill-alt"></i>
					</div>
					<h6 class="mb-1 text-dark"><?= tr("Sales") ?></h6>
					<small class="text-muted"><?= tr("Process sales") ?> (Ctrl+S)</small>
				</div>
			</div>
		</a>
	</div>
	<?php endif; ?>

	<?php if (can("view-customers")): ?>
	<div class="col-lg-3 col-md-4 col-sm-6">
		<a href="<?= base_url('customers/') ?>" id="customers-shortcut" class="text-decoration-none">
			<div class="card h-100 border-0 shadow-sm transition-hover">
				<div class="card-body text-center p-4">
					<div class="icon-circle bg-info-light text-info mx-auto mb-3">
						<i class="fas fa-users"></i>
					</div>
					<h6 class="mb-1 text-dark"><?= tr("Customers") ?></h6>
					<small class="text-muted"><?= tr("Manage customers") ?> (Ctrl+C)</small>
				</div>
			</div>
		</a>
	</div>
	<?php endif; ?>

	<?php if (can("view-suppliers")): ?>
	<div class="col-lg-3 col-md-4 col-sm-6">
		<a href="<?= base_url('suppliers/') ?>" class="text-decoration-none">
			<div class="card h-100 border-0 shadow-sm transition-hover">
				<div class="card-body text-center p-4">
					<div class="icon-circle bg-warning-light text-warning mx-auto mb-3">
						<i class="fas fa-truck"></i>
					</div>
					<h6 class="mb-1 text-dark"><?= tr("Suppliers") ?></h6>
					<small class="text-muted"><?= tr("Manage suppliers") ?></small>
				</div>
			</div>
		</a>
	</div>
	<?php endif; ?>
</div>

<!-- Secondary Operations -->
<div class="row mb-4">
	<div class="col-12">
		<h6 class="text-muted mb-3"><?= tr("Operations") ?></h6>
	</div>
</div>

<div class="row g-3 mb-4">
	<?php if (can("view-quotations")): ?>
	<div class="col-lg-3 col-md-4 col-sm-6">
		<a href="<?= base_url('quotations/') ?>" class="text-decoration-none">
			<div class="card h-100 border-0 shadow-sm transition-hover">
				<div class="card-body text-center p-4">
					<div class="icon-circle bg-secondary-light text-secondary mx-auto mb-3">
						<i class="fas fa-file-alt"></i>
					</div>
					<h6 class="mb-1 text-dark"><?= tr("Quotations") ?></h6>
					<small class="text-muted"><?= tr("Create quotes") ?></small>
				</div>
			</div>
		</a>
	</div>
	<?php endif; ?>

	<?php if (can("view-purchases")): ?>
	<div class="col-lg-3 col-md-4 col-sm-6">
		<a href="<?= base_url('purchases/') ?>" class="text-decoration-none">
			<div class="card h-100 border-0 shadow-sm transition-hover">
				<div class="card-body text-center p-4">
					<div class="icon-circle bg-primary-light text-primary mx-auto mb-3">
						<i class="fas fa-shopping-cart"></i>
					</div>
					<h6 class="mb-1 text-dark"><?= tr("Purchases") ?></h6>
					<small class="text-muted"><?= tr("Track purchases") ?></small>
				</div>
			</div>
		</a>
	</div>
	<?php endif; ?>

	<?php if (can("view-expenses")): ?>
	<div class="col-lg-3 col-md-4 col-sm-6">
		<a href="<?= base_url('expenses/') ?>" class="text-decoration-none">
			<div class="card h-100 border-0 shadow-sm transition-hover">
				<div class="card-body text-center p-4">
					<div class="icon-circle bg-danger-light text-danger mx-auto mb-3">
						<i class="fas fa-comment-dollar"></i>
					</div>
					<h6 class="mb-1 text-dark"><?= tr("Expenses") ?></h6>
					<small class="text-muted"><?= tr("Track expenses") ?></small>
				</div>
			</div>
		</a>
	</div>
	<?php endif; ?>
</div>

<!-- Reports Section -->
<?php if (can("view-reports")): ?>
<div class="row mb-4">
	<div class="col-12">
		<h6 class="text-muted mb-3"><?= tr("Reports & Analytics") ?></h6>
	</div>
</div>

<div class="row g-3 mb-4">
	<div class="col-lg-3 col-md-4 col-sm-6">
		<a href="<?= base_url('reports/sales.php') ?>" id="reports-shortcut" class="text-decoration-none">
			<div class="card h-100 border-0 shadow-sm transition-hover">
				<div class="card-body text-center p-4">
					<div class="icon-circle bg-success-light text-success mx-auto mb-3">
						<i class="fas fa-chart-line"></i>
					</div>
					<h6 class="mb-1 text-dark"><?= tr("Sales Report") ?></h6>
					<small class="text-muted"><?= tr("Sales analytics") ?> (Ctrl+R)</small>
				</div>
			</div>
		</a>
	</div>

	<div class="col-lg-3 col-md-4 col-sm-6">
		<a href="<?= base_url('reports/stock/') ?>" class="text-decoration-none">
			<div class="card h-100 border-0 shadow-sm transition-hover">
				<div class="card-body text-center p-4">
					<div class="icon-circle bg-warning-light text-warning mx-auto mb-3">
						<i class="fas fa-boxes"></i>
					</div>
					<h6 class="mb-1 text-dark"><?= tr("Stock Report") ?></h6>
					<small class="text-muted"><?= tr("Inventory status") ?></small>
				</div>
			</div>
		</a>
	</div>

	<div class="col-lg-3 col-md-4 col-sm-6">
		<a href="<?= base_url('reports/prof_loss.php') ?>" class="text-decoration-none">
			<div class="card h-100 border-0 shadow-sm transition-hover">
				<div class="card-body text-center p-4">
					<div class="icon-circle bg-info-light text-info mx-auto mb-3">
						<i class="fas fa-chart-bar"></i>
					</div>
					<h6 class="mb-1 text-dark"><?= tr("Summary") ?></h6>
					<small class="text-muted"><?= tr("Profit & Loss") ?></small>
				</div>
			</div>
		</a>
	</div>

	<div class="col-lg-3 col-md-4 col-sm-6">
		<a href="<?= base_url('reports/products/') ?>" class="text-decoration-none">
			<div class="card h-100 border-0 shadow-sm transition-hover">
				<div class="card-body text-center p-4">
					<div class="icon-circle bg-primary-light text-primary mx-auto mb-3">
						<i class="fas fa-chart-pie"></i>
					</div>
					<h6 class="mb-1 text-dark"><?= tr("Items Report") ?></h6>
					<small class="text-muted"><?= tr("Product analytics") ?></small>
				</div>
			</div>
		</a>
	</div>
</div>
<?php endif; ?>

<!-- Administrative Section -->
<?php if (can("manage-users") || can("settings")): ?>
<div class="row mb-4">
	<div class="col-12">
		<h6 class="text-muted mb-3"><?= tr("Administration") ?></h6>
	</div>
</div>

<div class="row g-3 mb-4">
	<?php if (can("manage-users")): ?>
	<div class="col-lg-3 col-md-4 col-sm-6">
		<a href="<?= base_url('users/') ?>" class="text-decoration-none">
			<div class="card h-100 border-0 shadow-sm transition-hover">
				<div class="card-body text-center p-4">
					<div class="icon-circle bg-secondary-light text-secondary mx-auto mb-3">
						<i class="fas fa-user-cog"></i>
					</div>
					<h6 class="mb-1 text-dark"><?= tr("Users") ?></h6>
					<small class="text-muted"><?= tr("Manage users") ?></small>
				</div>
			</div>
		</a>
	</div>
	<?php endif; ?>

	<?php if (can("settings")): ?>
	<div class="col-lg-3 col-md-4 col-sm-6">
		<a href="<?= base_url('settings/') ?>" class="text-decoration-none">
			<div class="card h-100 border-0 shadow-sm transition-hover">
				<div class="card-body text-center p-4">
					<div class="icon-circle bg-secondary-light text-secondary mx-auto mb-3">
						<i class="fas fa-cog"></i>
					</div>
					<h6 class="mb-1 text-dark"><?= tr("Settings") ?></h6>
					<small class="text-muted"><?= tr("System settings") ?></small>
				</div>
			</div>
		</a>
	</div>
	<?php endif; ?>
</div>
<?php endif; ?>

<style>
/* Custom styles for dashboard shortcuts */
.bg-gradient-primary {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.icon-circle {
	width: 60px;
	height: 60px;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 1.5rem;
}

.bg-primary-light {
	background-color: rgba(0, 123, 255, 0.1);
}

.bg-success-light {
	background-color: rgba(40, 167, 69, 0.1);
}

.bg-info-light {
	background-color: rgba(23, 162, 184, 0.1);
}

.bg-warning-light {
	background-color: rgba(255, 193, 7, 0.1);
}

.bg-danger-light {
	background-color: rgba(220, 53, 69, 0.1);
}

.bg-secondary-light {
	background-color: rgba(108, 117, 125, 0.1);
}

.transition-hover {
	transition: all 0.3s ease;
}

.transition-hover:hover {
	transform: translateY(-5px);
	box-shadow: 0 0.75rem 1.5rem rgba(0, 0, 0, 0.15) !important;
}

.text-white-50 {
	color: rgba(255, 255, 255, 0.5) !important;
}

.g-3 > * {
	padding: 0.75rem;
}

/* Ripple effect */
.ripple {
	position: absolute;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.6);
	transform: scale(0);
	animation: ripple-animation 0.6s linear;
	pointer-events: none;
}

@keyframes ripple-animation {
	to {
		transform: scale(4);
		opacity: 0;
	}
}

@media (max-width: 576px) {
	.icon-circle {
		width: 50px;
		height: 50px;
		font-size: 1.25rem;
	}

	.card-body.p-4 {
		padding: 1.5rem !important;
	}
}
</style>

<script>
$(document).ready(function() {
	// Add keyboard shortcuts for quick navigation
	$(document).keydown(function(e) {
		// Only trigger if no input/textarea is focused
		if (!$('input, textarea, select').is(':focus')) {
			switch(e.which) {
				case 80: // P key for Products
					if (e.ctrlKey && $('#products-shortcut').length) {
						e.preventDefault();
						window.location.href = $('#products-shortcut').attr('href');
					}
					break;
				case 83: // S key for Sales
					if (e.ctrlKey && $('#sales-shortcut').length) {
						e.preventDefault();
						window.location.href = $('#sales-shortcut').attr('href');
					}
					break;
				case 67: // C key for Customers
					if (e.ctrlKey && $('#customers-shortcut').length) {
						e.preventDefault();
						window.location.href = $('#customers-shortcut').attr('href');
					}
					break;
				case 82: // R key for Reports
					if (e.ctrlKey && $('#reports-shortcut').length) {
						e.preventDefault();
						window.location.href = $('#reports-shortcut').attr('href');
					}
					break;
			}
		}
	});

	// Add tooltips to shortcut cards
	$('.transition-hover').each(function() {
		const $card = $(this);
		const title = $card.find('h6').text();
		const description = $card.find('small').text();

		$card.attr('title', title + ' - ' + description);
		$card.tooltip({
			placement: 'top',
			trigger: 'hover'
		});
	});

	// Update time every minute
	function updateTime() {
		const now = new Date();
		const timeString = now.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
		$('.current-time').text(timeString);
	}

	// Update time immediately and then every minute
	updateTime();
	setInterval(updateTime, 60000);

	// Add ripple effect to cards on click
	$('.transition-hover').on('click', function(e) {
		const $card = $(this);
		const ripple = $('<span class="ripple"></span>');

		$card.css('position', 'relative');
		$card.append(ripple);

		const x = e.pageX - $card.offset().left;
		const y = e.pageY - $card.offset().top;

		ripple.css({
			left: x,
			top: y
		}).addClass('animate');

		setTimeout(() => {
			ripple.remove();
		}, 600);
	});
});
</script>

<?php _layout_end() ?>