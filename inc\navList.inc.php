<?php




// end customer notif

// sup_pay notifi



$sup_pay_notif_count=0;

$payments_badge = 0;
$shipment_badge =0;

$stocking_badge = 0;
// calendar 




$calendar_count=0;




	$navList=[
				[
					"title"=>"Products",
					"icon"=>"fas fa-barcode",
					"role"=>"view-products",
					"active"=>0,
					"badge"=>$stocking_badge,
					"links"=>[
								
								[
									"title"=>"Products",
									"attr"=>'href="'.$app_url.'/products/"',
									"type"=>"link",
									"role"=>"view-products",
									"active"=>("products"),
									"icon"=>"",
									"badge"=>0,
								],
						
								
						

								[
									"title"=>"Categories",
									"attr"=>'href="'.$app_url.'/products/categories.php"',
									"type"=>"link",
									"role"=>"manage-products",
									"active"=>("types-categories"),
									"icon"=>"",
								],
								[
									"title"=>"Brands",
									"attr"=>'href="'.$app_url.'/products/brands.php"',
									"type"=>"link",
									"role"=>"manage-products",
									"active"=>("types-brands"),
									"icon"=>"",
								],
					
							

								
								
					
							]
				],
				[
					"title"=>"Sales",
					"icon"=>"fas fa-money-bill-alt",
					"role"=>"view-sales|view-payments|view-refunds",
					"active"=>0,
					"badge"=>$payments_badge + $shipment_badge,
					"links"=>[
								[
									"title"=>"Sales",
									"attr"=>'href="'.$app_url.'/sales/"',
									"type"=>"link",
									"role"=>"view-sales",
									"active"=>("sales"),
									"icon"=>"",
								],
								[
									"title"=>"Quotations",
									"attr"=>'href="'.$app_url.'/quotations/"',
									"type"=>"link",
									"role"=>"view-quotations",
									"active"=>0,
									"icon"=>"",
								],
								[
									"title"=>"Taxes",
									"attr"=>'href="'.$app_url.'/types/taxes/"',
									"type"=>"link",
									"role"=>"manage-taxes",
									"active"=>("taxes"),
									"icon"=>"",
								],
							
								
							
							]
				],
		
				[
					"title"=>"Expenses",
					"icon"=>"fas fa-comment-dollar",
					"active"=>"expenses",
					"role"=>"view-expenses|view-purchases",
					"links"=>[
							
								[
									"title"=>"Expenses",
									"attr"=>'href="'.$app_url.'/expenses/"',
									"type"=>"link",
									"active"=>("expenses"),
									"role"=>"view-expenses",
									"icon"=>"",
								],
								[
									"title"=>"Purchases",
									"attr"=>'href="'.$app_url.'/purchases/"',
									"type"=>"link",
									"active"=>("purchases"),
									"role"=>"view-purchases",
									"icon"=>"",
								],
						
							]
				],
		

				[
					"title"=>"Customers",
					"icon"=>"fas fa-users",
					"role"=>"view-customers",
					"active"=>0,
					"badge"=>0,
					"links"=>[
								[
									"title"=>"Customers",
									"attr"=>'href="'.$app_url.'/customers/"',
									"type"=>"link",
									"role"=>"view-customers",
									"active"=>("customers"),
									"icon"=>"",
								],
						
			
				
							]
				],
			
				[
					"title"=>"Suppliers",
					"icon"=>"fas fa-truck",
					"active"=>0,
					"role"=>"view-suppliers",
					"badge"=>$sup_pay_notif_count,
					"links"=>[
								[
									"title"=>"Suppliers",
									"attr"=>'href="'.$app_url.'/suppliers/"',
									"type"=>"link",
									"role"=>"manage-suppliers",
									"active"=>("suppliers"),
									"icon"=>"",
								],
								
						
							]
				],
	
				[
					"title"=>"Users",
					"icon"=>"fas fa-users",
					"active"=>0,
					"role"=>"manage-users",
					"links"=>[
								[
									"title"=>"Users",
									"attr"=>'href="'.$app_url.'/users/"',
									"type"=>"link",
									"role"=>"manage-users",
									"active"=>("users"),
									"icon"=>"",
								],
								[
									"title"=>"Roles",
									"attr"=>'href="'.$app_url.'/roles/"',
									"type"=>"link",
									"role"=>"manage-users",
									"active"=>("roles"),
									"icon"=>"",
								],
								
							]
				],
				[
					"title"=>"Reports",
					"icon"=>"fas fa-chart-bar",
					"active"=>0,
					"role"=>"view-reports",
					"links"=>[
								[
									"title"=>"Purchase report",
									"attr"=>'href="'.$app_url.'/reports/purchase.php"',
									"type"=>"link",
									"role"=>"view-reports",
									"active"=>("purchases report"),
									"icon"=>"",
								],
								[
									"title"=>"Sales report",
									"attr"=>'href="'.$app_url.'/reports/sales.php"',
									"type"=>"link",
									"role"=>"view-reports",
									"active"=>("sales report"),
									"icon"=>"",
								],
								[
									"title"=>"Items report",
									"attr"=>'href="'.$app_url.'/reports/products/"',
									"type"=>"link",
									"role"=>"view-reports",
									"active"=>("items report"),
									"icon"=>"",
								],

								[
									"title"=>"Stock report",
									"attr"=>'href="'.$app_url.'/reports/stock/"',
									"type"=>"link",
									"role"=>"view-reports",
									"active"=>("stock report"),
									"icon"=>"",
								],
			
								[
									"title"=>"Summary",
									"attr"=>'href="'.$app_url.'/reports/prof_loss.php"',
									"type"=>"link",
									"role"=>"view-reports",
									"active"=>("profit report"),
									"icon"=>"",
								],
							],
						
				],

				[
					"title"=>"Settings",
					"icon"=>"fas fa-cog",
					"active"=>0,
					"role"=>"settings",
					"links"=>[
						[
							"title"=>"Settings",
							"attr"=>'href="'.$app_url.'/settings/"',
							"type"=>"link",
							"role"=>"settings",
							"active"=>("settings"),
							"icon"=>"",
						],
						[
							"title"=>"Branches",
							"attr"=>'href="'.$app_url.'/settings/branch.php"',
							"type"=>"link",
							"role"=>"settings",
							"active"=>("settings"),
							"icon"=>"",
						]
					]
				]
			];





			// if (count($product->expalert)) {
			// 	$navList[0]["links"][5]=[
			// 						"title"=>"Expired",
			// 						"attr"=>'href="'.$app_url.'/products/expiryalert.php"',
			// 						"type"=>"link",
			// 						"role"=>"products",
			// 						"icon"=>"",
			// 						"badge"=>count($product->expalert),
			// 					];
			// }