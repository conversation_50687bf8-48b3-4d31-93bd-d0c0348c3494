<?php  require_once(__DIR__ . '/navList.inc.php'); ?>
<div class="sidebar sidebar-dark  sidebar-main  sidebar-expand-md bg-primary">
    <!-- Sidebar mobile toggler -->
    <div class="sidebar-mobile-toggler text-center">
        <a href="#" class="sidebar-mobile-main-toggle">
            <i class="icon-arrow-right8"></i>
        </a>
        Navigation
        <a href="#" class="sidebar-mobile-expand">
            <i class="icon-screen-full"></i>
            <i class="icon-screen-normal"></i>
        </a>
    </div>
    <!-- /sidebar mobile toggler -->
    <!-- Sidebar content -->
    <div class="sidebar-content">


        <!-- Main navigation -->
        <div class="card card-sidebar-mobile">
            <ul class="nav nav-sidebar" data-nav-type="accordion">


                <li class="nav-item">
                    <a href="<?= $app_url ?>" class="nav-link  ">
                        <i class="icon-home4"></i>
                        <span>
                            <?= tr("Dashboard") ?>
                            <!-- <span class="d-block font-weight-normal opacity-50">No active orders</span>  -->
                        </span>
                    </a>
                </li>


                <?php foreach ($navList as $key => $item) : ?>
					<?php 
						$active=$item['active']==($page['active']??'') || $item['links'][0]['active']==($page['active']??''); 
						$active_link=0;
						
						?>

                <?php if (can($item['role'])) : ?>
                <li
                    class="nav-item <?= count($item['links'])>1?'nav-item-submenu':'' ?> <?= ($active)?'active  nav-item-expanded nav-item-open':'' ?>">
                    <?php if (count($item['links'])==1): ?>

                    <a class="nav-link " <?= ($item['links'][0]['attr']) ?>>
                        <?php if (strlen($item['icon'])): ?>
                        <i class="<?= $item['icon'] ?>"></i>
                        <?php endif ?> <span><?= tr($item['title']) ?></span>
                        <?php if (isset($item['links'][0]['badge']) && $item['links'][0]['badge']>0): ?>
                        <span
                            class="badge badge-light align-self-center ml-auto"><?= $item['links'][0]['badge'] ?></span>
                        <?php endif ?>
                    </a>


                    <?php else: ?>
                    <a href="#" class="nav-link    <?= ($active)?'active':'collapsed' ?>">
                        <i class="<?= escap($item['icon']) ?>"></i>
                        <span><?= tr($item['title']) ?></span>
                        <?php if (isset($item['badge']) && $item['badge']>0): ?>
                        <span class="badge badge-light align-self-center ml-auto"><?= $item['badge'] ?></span>
                        <?php endif ?>

                    </a>

                    <ul class="nav nav-group-sub " data-submenu-title="<?= tr($item['title']) ?>">
                        <?php foreach ($item['links'] as $key => $link) : ?>

                        <?php if (can($link['role'])) : ?>
                        <li class="nav-item  ">

                            <a class="nav-link pl-1 <?= ($active_link)?' active':'' ?>" <?= ($link['attr']) ?>>
                                <?php if (strlen($link['icon'])): ?>
                                <i class="<?= escap($link['icon']) ?>"></i>
                                <?php endif ?> <span><?= tr($link['title']) ?></span>
                                <?php if (isset($link['badge']) && $link['badge']>0): ?>
                                <span class="badge badge-light align-self-center ml-auto"><?= $link['badge'] ?></span>
                                <?php endif ?>
                            </a>

                        </li>
                        <?php endif ?>


                        <?php endforeach ?>
                    </ul>

                    <?php endif ?>

                </li>
                <?php endif ?>
                <?php endforeach ?>


               

               



                <!-- /layout -->

            </ul>
        </div>
        <!-- /main navigation -->
    </div>
    <!-- /sidebar content -->
</div>