<?php
// Models
require(__DIR__."/../php/init.php");

use App\Models\Branch;


// Page settings
$page = [
    "title" => tr("Branch Settings"),
    "active" => "settings", // sidebar active link
];

// Check permissions
if (!can("settings") && !can("branches")) {
    die("403");
}

// Page navigation
$page_nav = [
    "reload" => 1, // show reload button
    "back" => base_url("settings"), // show back button
    "breadcrumb" => [ // show breadcrumb
        tr("Home") => base_url("dashboard.php"),
        tr("Settings") => base_url("settings"),
        tr("Branch Settings") => "",
    ],
];



// Handle form submission
if (is_post()) {


    $v = validate([
        tr("Branch Name") => ["name", "required|min:2|max:100"],
    ]);


    if(input('id')){

        $branch = Branch::find(input('id'));

    }
    else{
        $branch = new Branch();
    }


    $branch->fill($_POST);

    if($_FILES['invoice_header']){

        $file = storage()->save($_FILES['invoice_header']);
        if($file->saved()){
            $value= $file->id;
            $branch->options['invoice_header'] = $value;
        }else{
            $branch->options['invoice_header'] = '';
        }
    }

    $branch->save();


    _response([
        "success" => true,
        "message" => [tr("Updated successfully")],
        "action" => "location.reload()"
    ]);

}

$options = [
    'general' => [
        'title' => tr('General Settings'),
        'settings' => [
            'receipt_header' => [
                'type' => 'summernote',
                'label' => tr("Receipt Header"),
                'default' => '',
                'col' => 'col-md-12',
                'help' => tr("Custom header text for receipts"),
            ],
            'receipt_footer' => [
                'type' => 'textarea',
                'label' => tr("Receipt Footer"),
                'default' => '',
                'col' => 'col-md-12',
                'help' => tr("Custom footer text for receipts"),
            ],


  
        ]
    ],


    'contact' => [
        'title' => tr('Contact Information'),
        'settings' => [
            'branch_address' => [
                'type' => 'textarea',
                'label' => tr("Branch Address"),
                'default' => '',
                'col' => 'col-md-12',
                'help' => tr("Physical address of the branch"),
            ],
            'branch_phone' => [
                'type' => 'text',
                'label' => tr("Branch Phone"),
                'default' => '',
                'col' => 'col-md-6',
                'help' => tr("Branch contact phone number"),
            ],
            'branch_email' => [
                'type' => 'email',
                'label' => tr("Branch Email"),
                'default' => '',
                'col' => 'col-md-6',
                'help' => tr("Branch contact email address"),
            ],


        ]
    ],
];

?>

<!-- Layout start -->
<?php _layout_start() ?>

<div class="d-flex justify-content-between mb-3">
    <h3><?= tr("Branch Settings") ?></h3>
    <div>
        <a href="<?= base_url("settings/branch.php?new=1") ?>" class="btn btn-primary">
            <i class="fa fa-plus"></i> <?= tr("Add New Branch") ?>
        </a>
    </div>
</div>
<div class="row">
    <div class="col-md-4">
        <div class="card">
            <table class="table ">
                <thead class="bg-primary text-white">
                    <tr>
                        <th><?= tr("Branch Name") ?></th>
                        <td><?= tr("Actions") ?></td>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach (Branch::all() as $branch): ?>
                    <tr>
                        <td><?= $branch->name ?></td>
                        <td>
                            <a href="<?= base_url("settings/branch.php?i={$branch->id}") ?>"
                                class="btn btn-primary"><?= tr("Edit") ?></a>
                        </td>
                    </tr>
                    <?php endforeach ?>
                </tbody>
            </table>
        </div>
    </div>

    <div class="col-md-8">
        <?php if (isset($_GET['i'])): ?>
            <?php $branch = Branch::find($_GET['i']) ?>
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title"><?= tr("Edit Branch") ?> - <?= $branch->name ?></h5>
                    <form method="post" class="ajax" action="<?= base_url("settings/branch.php?id={$branch->id}") ?>">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="name"><?= tr("Branch Name") ?></label>
                                    <input type="text" name="name" class="form-control" value="<?= $branch->name ?>">
                                </div>
                            </div>
                        </div>

                        <?php foreach ($options as $section_key => $section): ?>
                            <?php if (count($options) > 1): ?>
                            <div class="mb-4">
                                <h5 class="border-bottom pb-2 mb-3"><?= $section['title'] ?></h5>
                            </div>
                            <?php endif; ?>

                            <div class="row">
                            <?php foreach ($section['settings'] as $key => $setting): ?>
                                <div class="<?= isset($setting['col']) ? $setting['col'] : 'col-md-6' ?> <?= $setting['type'] == 'checkbox' ? 'col-12' : '' ?>">
                                    <div class="form-group">
                                        <?php if ($setting['type'] == 'checkbox'): ?>
                                        <div class="form-check">
                                            <input type="hidden" name="options[<?= $key ?>]" value="0">
                                            <input type="checkbox" class="form-check-input" id="<?= $key ?>" name="options[<?= $key ?>]" value="1"
                                                <?= ($branch->options[$key] ?? $setting['default']) == '1' ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="<?= $key ?>">
                                                <?= $setting['label'] ?>
                                            </label>
                                        </div>
                                        <?php else: ?>
                                        <label for="<?= $key ?>"><?= $setting['label'] ?></label>

                                        <?php if ($setting['type'] == 'text'): ?>
                                        <input type="text" class="form-control" id="<?= $key ?>" name="options[<?= $key ?>]"
                                            value="<?= escap($branch->options[$key] ?? $setting['default']) ?>"
                                            placeholder="<?= $setting['label'] ?>">

                                        <?php elseif ($setting['type'] == 'email'): ?>
                                        <input type="email" class="form-control" id="<?= $key ?>" name="options[<?= $key ?>]"
                                            value="<?= escap($branch->options[$key] ?? $setting['default']) ?>"
                                            placeholder="<?= $setting['label'] ?>">

                                        <?php elseif ($setting['type'] == 'number'): ?>
                                        <input type="number" class="form-control" id="<?= $key ?>" name="options[<?= $key ?>]"
                                            value="<?= escap($branch->options[$key] ?? $setting['default']) ?>"
                                            placeholder="<?= $setting['label'] ?>" step="0.01">

                                        <?php elseif ($setting['type'] == 'select'): ?>
                                        <select class="form-control" id="<?= $key ?>" name="options[<?= $key ?>]">
                                            <?php foreach ($setting['options'] as $option_value => $option_label): ?>
                                            <option value="<?= $option_value ?>"
                                                <?= ($branch->options[$key] ?? $setting['default']) == $option_value ? 'selected' : '' ?>>
                                                <?= $option_label ?>
                                            </option>
                                            <?php endforeach; ?>
                                        </select>

                                        <?php elseif ($setting['type'] == 'textarea'): ?>
                                        <textarea class="form-control" id="<?= $key ?>" name="options[<?= $key ?>]" rows="4"
                                            placeholder="<?= $setting['label'] ?>"><?= escap($branch->options[$key] ?? $setting['default']) ?></textarea>

                                        <?php elseif ($setting['type'] == 'summernote'): ?>
                                        <textarea class="form-control summernote" id="<?= $key ?>" name="options[<?= $key ?>]" rows="4"
                                            placeholder="<?= $setting['label'] ?>"><?= escap($branch->options[$key] ?? $setting['default']) ?></textarea>

                                        <?php endif; ?>
                                        <?php endif; ?>

                                        <?php if (isset($setting['help'])): ?>
                                        <small class="form-text text-muted"><?= $setting['help'] ?></small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                            </div>
                        <?php endforeach; ?>

                        <button type="submit" class="btn btn-primary"><?= tr("Save") ?></button>
                    </form>
                </div>
            </div>
        <?php elseif (isset($_GET['new'])): ?>
            <?php $branch = new Branch(); ?>
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title"><?= tr("Add New Branch") ?></h5>
                    <form method="post" class="ajax" action="<?= base_url("settings/branch.php") ?>">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="name"><?= tr("Branch Name") ?></label>
                                    <input type="text" name="name" class="form-control" value="<?= $branch->name ?? '' ?>">
                                </div>
                            </div>
                        </div>

                        <?php foreach ($options as $section_key => $section): ?>
                            <?php if (count($options) > 1): ?>
                            <div class="mb-4">
                                <h5 class="border-bottom pb-2 mb-3"><?= $section['title'] ?></h5>
                            </div>
                            <?php endif; ?>

                            <div class="row">
                            <?php foreach ($section['settings'] as $key => $setting): ?>
                                <div class="<?= isset($setting['col']) ? $setting['col'] : 'col-md-6' ?> <?= $setting['type'] == 'checkbox' ? 'col-12' : '' ?>">
                                    <div class="form-group">
                                        <?php if ($setting['type'] == 'checkbox'): ?>
                                        <div class="form-check">
                                            <input type="hidden" name="options[<?= $key ?>]" value="0">
                                            <input type="checkbox" class="form-check-input" id="<?= $key ?>" name="options[<?= $key ?>]" value="1"
                                                <?= $setting['default'] == '1' ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="<?= $key ?>">
                                                <?= $setting['label'] ?>
                                            </label>
                                        </div>
                                        <?php else: ?>
                                        <label for="<?= $key ?>"><?= $setting['label'] ?></label>

                                        <?php if ($setting['type'] == 'text'): ?>
                                        <input type="text" class="form-control" id="<?= $key ?>" name="options[<?= $key ?>]"
                                            value="<?= escap($setting['default']) ?>"
                                            placeholder="<?= $setting['label'] ?>">

                                        <?php elseif ($setting['type'] == 'email'): ?>
                                        <input type="email" class="form-control" id="<?= $key ?>" name="options[<?= $key ?>]"
                                            value="<?= escap($setting['default']) ?>"
                                            placeholder="<?= $setting['label'] ?>">

                                        <?php elseif ($setting['type'] == 'number'): ?>
                                        <input type="number" class="form-control" id="<?= $key ?>" name="options[<?= $key ?>]"
                                            value="<?= escap($setting['default']) ?>"
                                            placeholder="<?= $setting['label'] ?>" step="0.01">

                                        <?php elseif ($setting['type'] == 'select'): ?>
                                        <select class="form-control" id="<?= $key ?>" name="options[<?= $key ?>]">
                                            <?php foreach ($setting['options'] as $option_value => $option_label): ?>
                                            <option value="<?= $option_value ?>"
                                                <?= $setting['default'] == $option_value ? 'selected' : '' ?>>
                                                <?= $option_label ?>
                                            </option>
                                            <?php endforeach; ?>
                                        </select>

                                        <?php elseif ($setting['type'] == 'textarea'): ?>
                                        <textarea class="form-control" id="<?= $key ?>" name="options[<?= $key ?>]" rows="4"
                                            placeholder="<?= $setting['label'] ?>"><?= escap($setting['default']) ?></textarea>

                                        <?php elseif ($setting['type'] == 'summernote'): ?>
                                        <textarea class="form-control summernote" id="<?= $key ?>" name="options[<?= $key ?>]" rows="4"
                                            placeholder="<?= $setting['label'] ?>"><?= escap($setting['default']) ?></textarea>

                                        <?php endif; ?>
                                        <?php endif; ?>

                                        <?php if (isset($setting['help'])): ?>
                                        <small class="form-text text-muted"><?= $setting['help'] ?></small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                            </div>
                        <?php endforeach; ?>

                        <button type="submit" class="btn btn-primary"><?= tr("Save") ?></button>
                    </form>
                </div>
            </div>
        <?php endif ?>
    </div>
</div>

<!-- Layout end -->
<?php _layout_end() ?>