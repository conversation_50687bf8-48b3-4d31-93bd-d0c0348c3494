<?php

use App\Models\Sale;
use App\Models\Customer;
use App\Models\Branch;
use App\Models\Payment;

require __DIR__."/../php/init.php";

$page = [
    "title" => tr("Sales Report"),
    "active" => "reports",
];

if (!can("view-reports")) {
    die("403");
}

$page_nav = [
    "reload" => 1,
    "breadcrumb" => [
        tr("Home") => base_url("dashboard.php"),
        tr("Reports") => base_url("reports/"),
        tr("Sales Report") => base_url("reports/sales.php"),
    ],
];

// Get filter parameters
$_from = input("from", date("Y-m-01")); // First day of current month
$_to = input("to", date("Y-m-t"));     // Last day of current month
$_status = input("status", "");
$_customer_id = input("customer_id", "");
$_branch_id = input("branch_id", "");
$_payment_status = input("payment_status", "");

// Build query
$query = Sale::query()
    ->with(['customer', 'branch', 'createdBy'])
    ->whereBetween('date', [$_from, $_to]);

// Apply filters
if (!empty($_status)) {
    $query->where('status', $_status);
}

if (!empty($_customer_id)) {
    $query->where('customer_id', $_customer_id);
}

if (!empty($_branch_id)) {
    $query->where('branch_id', $_branch_id);
} else {
    // Apply branch restrictions
    if (!can("branches")) {
        $query->where('branch_id', _branch()->id());
    }
}

// Get sales data
$sales = $query->orderBy('date', 'desc')->get();

// Calculate summary statistics
$summary = [
    'total_sales' => $sales->count(),
    'total_revenue' => $sales->sum('total_price'),
    'total_tax' => $sales->sum('total_tax'),
    'average_order_value' => $sales->count() > 0 ? $sales->sum('total_price') / $sales->count() : 0,
];

// Calculate payment statistics efficiently
$payment_stats = ['paid' => 0, 'unpaid' => 0, 'partially_paid' => 0, 'overpaid' => 0];

if ($sales->count() > 0) {
    // Get all payment amounts for the sales in one query
    $sale_ids = $sales->pluck('id')->toArray();
    $payments = Payment::where('reltype', Payment::RELTYPE_SALES)
                      ->whereIn('relid', $sale_ids)
                      ->selectRaw('relid, SUM(amount) as total_paid')
                      ->groupBy('relid')
                      ->pluck('total_paid', 'relid')
                      ->toArray();

    foreach ($sales as $sale) {
        $paid_amount = $payments[$sale->id] ?? 0;

        if ($paid_amount == 0) {
            $payment_stats['unpaid']++;
        } elseif ($paid_amount < $sale->total_price) {
            $payment_stats['partially_paid']++;
        } elseif ($paid_amount == $sale->total_price) {
            $payment_stats['paid']++;
        } else {
            $payment_stats['overpaid']++;
        }
    }
}

// Status breakdown
$status_breakdown = $sales->groupBy('status')->map(function ($group) {
    return [
        'count' => $group->count(),
        'total' => $group->sum('total_price')
    ];
});

// Get customers for filter dropdown
$customers = Customer::orderBy('name')->get();

// Get branches for filter dropdown (if user has permission)
$branches = [];
if (can("branches")) {
    $branches = Branch::orderBy('name')->get();
}

?>
<?php _layout_start() ?>

<!-- Filter Section -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><?= tr("Filter Options") ?></h5>
    </div>
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label class="form-label"><?= tr("From Date") ?></label>
                <input type="date" name="from" class="form-control" value="<?= $_from ?>">
            </div>
            <div class="col-md-3">
                <label class="form-label"><?= tr("To Date") ?></label>
                <input type="date" name="to" class="form-control" value="<?= $_to ?>">
            </div>
            <div class="col-md-2">
                <label class="form-label"><?= tr("Status") ?></label>
                <select name="status" class="form-control">
                    <option value=""><?= tr("All Statuses") ?></option>
                    <?php foreach (Sale::getStatuses() as $key => $status): ?>
                        <option value="<?= $key ?>" <?= $_status == $key ? 'selected' : '' ?>><?= $status ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label"><?= tr("Customer") ?></label>
                <select name="customer_id" class="form-control">
                    <option value=""><?= tr("All Customers") ?></option>
                    <?php foreach ($customers as $customer): ?>
                        <option value="<?= $customer->id ?>" <?= $_customer_id == $customer->id ? 'selected' : '' ?>><?= esc($customer->name) ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            <?php if (can("branches")): ?>
            <div class="col-md-2">
                <label class="form-label"><?= tr("Branch") ?></label>
                <select name="branch_id" class="form-control">
                    <option value=""><?= tr("All Branches") ?></option>
                    <?php foreach ($branches as $branch): ?>
                        <option value="<?= $branch->id ?>" <?= $_branch_id == $branch->id ? 'selected' : '' ?>><?= esc($branch->name) ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            <?php endif; ?>
            <div class="col-12">
                <button type="submit" class="btn btn-primary"><?= tr("Apply Filters") ?></button>
                <a href="<?= base_url('reports/sales.php') ?>" class="btn btn-secondary"><?= tr("Reset") ?></a>
                <button type="button" class="btn btn-success" onclick="exportToExcel()"><?= tr("Export Excel") ?></button>
                <button type="button" class="btn btn-info" onclick="window.print()"><?= tr("Print Report") ?></button>
                <?php if ($sales->count() > 0): ?>
                <span class="text-muted ms-3"><?= tr("Found") ?> <strong><?= number_format($sales->count()) ?></strong> <?= tr("sales records") ?></span>
                <?php endif; ?>
            </div>
        </form>
    </div>
</div>

<?php if ($sales->count() > 0): ?>
<!-- Summary Statistics -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="text-white-50 mb-1"><?= tr("Total Sales") ?></h6>
                        <h4 class="text-white mb-0"><?= number_format($summary['total_sales']) ?></h4>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-shopping-cart fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="text-white-50 mb-1"><?= tr("Total Revenue") ?></h6>
                        <h4 class="text-white mb-0"><?= _format_price($summary['total_revenue']) ?></h4>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-dollar-sign fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="text-white-50 mb-1"><?= tr("Average Order") ?></h6>
                        <h4 class="text-white mb-0"><?= _format_price($summary['average_order_value']) ?></h4>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-chart-line fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="text-white-50 mb-1"><?= tr("Total Tax") ?></h6>
                        <h4 class="text-white mb-0"><?= _format_price($summary['total_tax']) ?></h4>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-percentage fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Section -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><?= tr("Sales Trend") ?></h5>
            </div>
            <div class="card-body">
                <canvas id="salesTrendChart" height="100"></canvas>
            </div>
        </div>
    </div>
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><?= tr("Sales by Status") ?></h5>
            </div>
            <div class="card-body">
                <canvas id="statusChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Payment Status and Revenue Analysis -->
<div class="row mb-4">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><?= tr("Payment Status") ?></h5>
            </div>
            <div class="card-body">
                <canvas id="paymentStatusChart" height="150"></canvas>
            </div>
        </div>
    </div>
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><?= tr("Revenue Analysis") ?></h5>
            </div>
            <div class="card-body">
                <canvas id="revenueChart" height="150"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Detailed Sales Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><?= tr("Detailed Sales Data") ?></h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped" id="salesTable">
                <thead>
                    <tr>
                        <th><?= tr("ID") ?></th>
                        <th><?= tr("Date") ?></th>
                        <th><?= tr("Customer") ?></th>
                        <th><?= tr("Total") ?></th>
                        <th><?= tr("Tax") ?></th>
                        <th><?= tr("Status") ?></th>
                        <th><?= tr("Payment Status") ?></th>
                        <?php if (can("branches")): ?>
                        <th><?= tr("Branch") ?></th>
                        <?php endif; ?>
                        <th><?= tr("Actions") ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    // Get payment data efficiently for table display
                    $sale_ids = $sales->pluck('id')->toArray();
                    $table_payments = [];
                    if (!empty($sale_ids)) {
                        $table_payments = Payment::where('reltype', Payment::RELTYPE_SALES)
                                                ->whereIn('relid', $sale_ids)
                                                ->selectRaw('relid, SUM(amount) as total_paid')
                                                ->groupBy('relid')
                                                ->pluck('total_paid', 'relid')
                                                ->toArray();
                    }
                    ?>
                    <?php foreach ($sales as $sale): ?>
                        <?php $paid_amount = $table_payments[$sale->id] ?? 0; ?>
                        <tr>
                            <td><a href="<?= base_url("sales/view.php?i={$sale->id}") ?>">#<?= $sale->id ?></a></td>
                            <td><?= _d($sale->date) ?></td>
                            <td>
                                <?php if ($sale->customer): ?>
                                    <a href="<?= base_url("customers/update.php?i={$sale->customer->id}") ?>">
                                        <?= esc($sale->customer->name) ?>
                                    </a>
                                <?php else: ?>
                                    <span class="text-muted"><?= tr("Walk-in Customer") ?></span>
                                <?php endif; ?>
                            </td>
                            <td><?= _format_price($sale->total_price) ?></td>
                            <td><?= _format_price($sale->total_tax) ?></td>
                            <td><?= Sale::formatStatus($sale->status) ?></td>
                            <td><?= _render_payment_status($sale->total_price, $paid_amount) ?></td>
                            <?php if (can("branches")): ?>
                            <td><?= $sale->branch ? esc($sale->branch->name) : '-' ?></td>
                            <?php endif; ?>
                            <td>
                                <div class="btn-group">
                                    <a href="<?= base_url("sales/view.php?i={$sale->id}") ?>" class="btn btn-sm btn-outline-primary" title="<?= tr("View") ?>">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="<?= base_url("sales/prints/receipt.php?i={$sale->id}") ?>" target="_blank" class="btn btn-sm btn-outline-secondary" title="<?= tr("Print") ?>">
                                        <i class="fas fa-print"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?php else: ?>
<!-- No Data Found -->
<div class="card">
    <div class="card-body text-center py-5">
        <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
        <h5 class="text-muted"><?= tr("No sales data found") ?></h5>
        <p class="text-muted"><?= tr("Try adjusting your filter criteria to see sales data.") ?></p>
        <a href="<?= base_url('reports/sales.php') ?>" class="btn btn-primary"><?= tr("Reset Filters") ?></a>
    </div>
</div>
<?php endif; ?>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    // Initialize DataTable with basic functionality
    $('#salesTable').DataTable({
        responsive: true,
        pageLength: 25,
        order: [[1, 'desc']], // Sort by date descending
        language: {
            search: "<?= tr('Search') ?>:",
            lengthMenu: "<?= tr('Show') ?> _MENU_ <?= tr('entries') ?>",
            info: "<?= tr('Showing') ?> _START_ <?= tr('to') ?> _END_ <?= tr('of') ?> _TOTAL_ <?= tr('entries') ?>",
            paginate: {
                first: "<?= tr('First') ?>",
                last: "<?= tr('Last') ?>",
                next: "<?= tr('Next') ?>",
                previous: "<?= tr('Previous') ?>"
            }
        }
    });

    // Prepare chart data
    const salesData = <?= json_encode($sales->values()) ?>;
    const statusBreakdown = <?= json_encode($status_breakdown) ?>;
    const paymentStats = <?= json_encode($payment_stats) ?>;

    // Sales Trend Chart
    const salesTrendCtx = document.getElementById('salesTrendChart').getContext('2d');

    // Group sales by date for trend chart
    const salesByDate = {};
    salesData.forEach(sale => {
        const date = sale.date;
        if (!salesByDate[date]) {
            salesByDate[date] = { count: 0, total: 0 };
        }
        salesByDate[date].count++;
        salesByDate[date].total += parseFloat(sale.total_price);
    });

    const sortedDates = Object.keys(salesByDate).sort();
    const trendLabels = sortedDates;
    const trendCounts = sortedDates.map(date => salesByDate[date].count);
    const trendTotals = sortedDates.map(date => salesByDate[date].total);

    new Chart(salesTrendCtx, {
        type: 'line',
        data: {
            labels: trendLabels,
            datasets: [{
                label: '<?= tr("Sales Count") ?>',
                data: trendCounts,
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                yAxisID: 'y'
            }, {
                label: '<?= tr("Revenue") ?>',
                data: trendTotals,
                borderColor: 'rgb(255, 99, 132)',
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: '<?= tr("Date") ?>'
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: '<?= tr("Sales Count") ?>'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: '<?= tr("Revenue") ?>'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            }
        }
    });

    // Status Chart
    const statusCtx = document.getElementById('statusChart').getContext('2d');
    const statusLabels = Object.keys(statusBreakdown);
    const statusCounts = statusLabels.map(status => statusBreakdown[status].count);
    const statusColors = ['#28a745', '#ffc107', '#6c757d'];

    new Chart(statusCtx, {
        type: 'doughnut',
        data: {
            labels: statusLabels.map(status => {
                const statuses = {
                    'delivered': '<?= tr("Delivered") ?>',
                    'pending': '<?= tr("Pending") ?>',
                    'draft': '<?= tr("Draft") ?>'
                };
                return statuses[status] || status;
            }),
            datasets: [{
                data: statusCounts,
                backgroundColor: statusColors,
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Payment Status Chart
    const paymentCtx = document.getElementById('paymentStatusChart').getContext('2d');
    const paymentLabels = ['<?= tr("Paid") ?>', '<?= tr("Unpaid") ?>', '<?= tr("Partially Paid") ?>', '<?= tr("Overpaid") ?>'];
    const paymentCounts = [
        paymentStats.paid || 0,
        paymentStats.unpaid || 0,
        paymentStats.partially_paid || 0,
        paymentStats.overpaid || 0
    ];
    const paymentColors = ['#28a745', '#dc3545', '#ffc107', '#17a2b8'];

    new Chart(paymentCtx, {
        type: 'pie',
        data: {
            labels: paymentLabels,
            datasets: [{
                data: paymentCounts,
                backgroundColor: paymentColors,
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Revenue Analysis Chart
    const revenueCtx = document.getElementById('revenueChart').getContext('2d');
    const revenueData = {
        labels: ['<?= tr("Gross Revenue") ?>', '<?= tr("Tax Amount") ?>', '<?= tr("Net Revenue") ?>'],
        datasets: [{
            label: '<?= tr("Amount") ?>',
            data: [
                <?= $summary['total_revenue'] ?>,
                <?= $summary['total_tax'] ?>,
                <?= $summary['total_revenue'] - $summary['total_tax'] ?>
            ],
            backgroundColor: ['#007bff', '#ffc107', '#28a745'],
            borderWidth: 2
        }]
    };

    new Chart(revenueCtx, {
        type: 'bar',
        data: revenueData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: '<?= tr("Amount") ?>'
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
});

// Export to Excel function
function exportToExcel() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'excel');
    window.location.href = '<?= base_url("reports/sales-export.php") ?>?' + params.toString();
}
</script>

<style>
.text-white-50 {
    color: rgba(255, 255, 255, 0.5) !important;
}

.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1rem;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.btn-group .btn {
    margin-right: 0.25rem;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

@media (max-width: 768px) {
    .col-md-6 {
        margin-bottom: 1rem;
    }

    .table-responsive {
        font-size: 0.875rem;
    }
}

/* Print styles */
@media print {
    .btn, .card-header .btn, form {
        display: none !important;
    }

    .card {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
        page-break-inside: avoid;
    }

    .card-header {
        background-color: #f8f9fa !important;
        -webkit-print-color-adjust: exact;
    }

    canvas {
        max-height: 300px !important;
    }

    .table {
        font-size: 0.8rem;
    }

    .page-break {
        page-break-before: always;
    }
}
</style>

<?php _layout_end() ?>